"""
Admin configuration for subscriptions app.
"""
from django.contrib import admin
from .models import SubscriptionPlan, ActiveSubscription, PaymentTransaction


class SubscriptionPlanLocationInline(admin.TabularInline):
    """
    Inline для управления локациями в тарифных планах.

    PURPOSE:
      - Обеспечивает удобное управление привязкой локаций к планам
      - Позволяет настраивать дефолтные локации прямо в плане
      - Упрощает администрирование доступа к серверам

    AAG (Actor -> Action -> Goal):
      - Администратор -> Настраивает локации для плана -> Контролирует доступ пользователей
    """
    from vpn.models import SubscriptionPlanLocation
    model = SubscriptionPlanLocation
    extra = 1
    fields = ('location', 'is_default')
    autocomplete_fields = ('location',)
    verbose_name = "Available Location"
    verbose_name_plural = "Available Locations"


@admin.register(SubscriptionPlan)
class SubscriptionPlanAdmin(admin.ModelAdmin):
    """Admin interface for SubscriptionPlan model."""

    list_display = ('name', 'price', 'currency', 'duration_days', 'traffic_limit_gb', 'max_devices', 'is_active', 'is_trial')
    list_filter = ('is_active', 'is_trial', 'currency')
    search_fields = ('name', 'description')
    ordering = ('price',)
    inlines = [SubscriptionPlanLocationInline]

    fieldsets = (
        (None, {'fields': ('name', 'description')}),
        ('Pricing', {'fields': ('price', 'currency')}),
        ('Limits', {'fields': ('duration_days', 'traffic_limit_gb', 'max_devices')}),
        ('Status', {'fields': ('is_active', 'is_trial')}),
    )


@admin.register(ActiveSubscription)
class ActiveSubscriptionAdmin(admin.ModelAdmin):
    """
    Административная панель для управления активными подписками.

    PURPOSE:
      - Обеспечивает управление активными подписками пользователей
      - Предоставляет информацию о сроках действия и статусе
      - Включает действия для массового управления подписками

    AAG (Actor -> Action -> Goal):
      - Администратор -> Управляет подписками -> Контролирует доступ к VPN-сервису
      - Поддержка -> Помогает с подписками -> Решает проблемы пользователей
    """

    list_display = ('user_display', 'plan', 'start_date', 'end_date', 'is_active', 'days_remaining_display', 'auto_renew')
    list_filter = ('is_active', 'auto_renew', 'plan', 'created_at')
    search_fields = ('user__email', 'plan__name')
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'
    actions = ['extend_subscriptions']

    fieldsets = (
        (None, {'fields': ('user', 'plan')}),
        ('Subscription Period', {'fields': ('start_date', 'end_date')}),
        ('Status', {'fields': ('is_active', 'auto_renew')}),
        ('Payment', {'fields': ('payment_method',)}),
    )

    def user_display(self, obj):
        """Отображает пользователя с учетом анонимных."""
        if obj.user.is_anonymous:
            return f"Anonymous {str(obj.user.id)[:8]}"
        return obj.user.email or f"User {str(obj.user.id)[:8]}"

    user_display.short_description = "User"
    user_display.admin_order_field = 'user__email'

    def days_remaining_display(self, obj):
        """Display days remaining in a user-friendly format."""
        if obj.is_expired:
            return "Expired"
        return f"{obj.days_remaining} days"
    days_remaining_display.short_description = "Days Remaining"

    def extend_subscriptions(self, request, queryset):
        """
        Продлевает выбранные подписки на 30 дней.

        PURPOSE:
          - Позволяет быстро продлить подписки пользователей
          - Упрощает компенсацию за проблемы с сервисом
          - Обеспечивает гибкое управление подписками

        ARGS:
          - request: HTTP запрос
          - queryset: QuerySet выбранных подписок
        """
        from datetime import timedelta

        updated_count = 0
        for subscription in queryset:
            subscription.end_date += timedelta(days=30)
            subscription.save()
            updated_count += 1

        self.message_user(
            request,
            f"Successfully extended {updated_count} subscription(s) by 30 days."
        )

    extend_subscriptions.short_description = "Extend selected subscriptions by 30 days"


@admin.register(PaymentTransaction)
class PaymentTransactionAdmin(admin.ModelAdmin):
    """Admin interface for PaymentTransaction model."""
    
    list_display = ('user', 'plan', 'amount', 'currency', 'payment_method', 'status', 'created_at')
    list_filter = ('status', 'payment_method', 'payment_provider', 'currency', 'created_at')
    search_fields = ('user__email', 'external_transaction_id', 'plan__name')
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (None, {'fields': ('user', 'subscription', 'plan')}),
        ('Payment Details', {'fields': ('amount', 'currency', 'payment_method', 'payment_provider')}),
        ('Transaction Info', {'fields': ('external_transaction_id', 'status')}),
        ('Timestamps', {'fields': ('created_at', 'updated_at')}),
    )
