"""
Serializers for subscriptions app.
"""
from rest_framework import serializers
from .models import SubscriptionPlan, ActiveSubscription, PaymentTransaction


class SubscriptionPlanSerializer(serializers.ModelSerializer):
    """
    Сериализатор для тарифных планов.
    
    PURPOSE:
      - Отображает информацию о доступных тарифах
      - Скрывает внутренние поля от публичного API
      - Форматирует данные для клиентских приложений
    """
    
    class Meta:
        model = SubscriptionPlan
        fields = (
            'id', 'name', 'description', 'price', 'currency', 
            'duration_days', 'traffic_limit_gb', 'max_devices', 'is_trial'
        )


class ActiveSubscriptionSerializer(serializers.ModelSerializer):
    """
    Сериализатор для активных подписок.
    
    PURPOSE:
      - Отображает информацию о текущей подписке пользователя
      - Включает данные о тарифном плане
      - Показывает статус и сроки действия
    """
    plan = SubscriptionPlanSerializer(read_only=True)
    days_remaining = serializers.ReadOnlyField()
    is_expired = serializers.ReadOnlyField()
    
    class Meta:
        model = ActiveSubscription
        fields = (
            'id', 'plan', 'start_date', 'end_date', 'is_active', 
            'auto_renew', 'days_remaining', 'is_expired', 'created_at'
        )


class PaymentTransactionSerializer(serializers.ModelSerializer):
    """
    Сериализатор для платежных транзакций.
    
    PURPOSE:
      - Отображает информацию о платежах пользователя
      - Обеспечивает аудит финансовых операций
      - Скрывает чувствительную информацию
    """
    plan = SubscriptionPlanSerializer(read_only=True)
    
    class Meta:
        model = PaymentTransaction
        fields = (
            'id', 'plan', 'amount', 'currency', 'payment_method', 
            'status', 'created_at', 'updated_at'
        )
        # Скрываем чувствительные поля
        read_only_fields = ('external_transaction_id', 'payment_provider')


class PurchaseRequestSerializer(serializers.Serializer):
    """
    Сериализатор для запроса покупки подписки.

    PURPOSE:
      - Валидирует данные запроса на покупку
      - Обеспечивает типизацию для OpenAPI схемы
      - Проверяет корректность входных параметров
    """
    plan_id = serializers.UUIDField(help_text="ID тарифного плана")
    payment_method = serializers.ChoiceField(
        choices=['card', 'crypto', 'paypal'],
        default='card',
        help_text="Метод оплаты"
    )


class PurchaseResponseSerializer(serializers.Serializer):
    """
    Сериализатор для ответа на запрос покупки.

    PURPOSE:
      - Стандартизирует ответ API покупки
      - Предоставляет информацию о транзакции
      - Включает ссылки для завершения платежа
    """
    success = serializers.BooleanField()
    transaction_id = serializers.UUIDField()
    payment_url = serializers.URLField(allow_null=True)
    message = serializers.CharField()


class CurrentSubscriptionResponseSerializer(serializers.Serializer):
    """
    Сериализатор для ответа с информацией о текущей подписке.

    PURPOSE:
      - Стандартизирует ответ API текущей подписки
      - Включает полную информацию о подписке и статистике
      - Обеспечивает типизацию для OpenAPI схемы
    """
    success = serializers.BooleanField()
    subscription = ActiveSubscriptionSerializer()
    usage_stats = serializers.DictField(allow_null=True)
