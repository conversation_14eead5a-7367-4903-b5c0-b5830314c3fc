"""
Views for subscriptions app.
"""
import logging
from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from django.utils import timezone
from django.db import transaction
from datetime import timedelta
from drf_spectacular.utils import extend_schema, OpenApiExample
from .models import SubscriptionPlan, ActiveSubscription, SubscriptionDevice
from .serializers import (
    SubscriptionPlanSerializer,
    ActiveSubscriptionSerializer,
    PurchaseRequestSerializer,
    PurchaseResponseSerializer,
    CurrentSubscriptionResponseSerializer,
    SubscriptionActivationSerializer,
    SubscriptionActivationResponseSerializer,
    SubscriptionErrorResponseSerializer
)
from promo.models import PromoCode
from accounts.models import UserDevice
from vpn.services import HiddifyApiService

logger = logging.getLogger(__name__)


@extend_schema(tags=['Subscriptions'])
class SubscriptionPlanListView(generics.ListAPIView):
    """
    Список доступных тарифных планов.
    
    PURPOSE:
      - Отображает все активные тарифные планы
      - Позволяет пользователям выбрать подходящий тариф
      - Предоставляет информацию о ценах и лимитах
    """
    queryset = SubscriptionPlan.objects.filter(is_active=True)
    serializer_class = SubscriptionPlanSerializer
    permission_classes = []  # Публичный доступ для просмотра планов


@extend_schema(
    tags=['Subscriptions'],
    responses={
        200: CurrentSubscriptionResponseSerializer,
        404: OpenApiExample(
            'No subscription',
            value={'error': 'No active subscription found'}
        )
    }
)
class CurrentSubscriptionView(APIView):
    """
    Информация о текущей подписке пользователя.

    PURPOSE:
      - Отображает активную подписку пользователя
      - Показывает статистику использования
      - Предоставляет информацию о сроках действия
    """
    permission_classes = [IsAuthenticated]
    serializer_class = CurrentSubscriptionResponseSerializer
    
    def get(self, request):
        """Получение информации о текущей подписке."""
        try:
            subscription = ActiveSubscription.objects.get(
                user=request.user,
                is_active=True,
                start_date__lte=timezone.now(),
                end_date__gte=timezone.now()
            )
            
            # Получаем информацию о трафике из Hiddify Link
            traffic_info = {}
            try:
                hiddify_link = request.user.hiddify_link
                traffic_info = {
                    'traffic_used_gb': round(hiddify_link.traffic_used_bytes / (1024**3), 2) if hiddify_link.traffic_used_bytes else 0,
                    'traffic_limit_gb': subscription.plan.traffic_limit_gb,
                    'last_sync': hiddify_link.last_traffic_sync.isoformat() if hiddify_link.last_traffic_sync else None
                }
            except:
                pass
            
            serializer = ActiveSubscriptionSerializer(subscription)
            response_data = serializer.data
            response_data['traffic_info'] = traffic_info
            
            return Response(response_data)
            
        except ActiveSubscription.DoesNotExist:
            return Response({
                'error': 'No active subscription found'
            }, status=status.HTTP_404_NOT_FOUND)


@extend_schema(
    tags=['Subscriptions'],
    request=PurchaseRequestSerializer,
    responses={
        200: PurchaseResponseSerializer,
        400: OpenApiExample(
            'Invalid request',
            value={'error': 'Invalid plan_id or payment_method'}
        )
    }
)
class PurchaseSubscriptionView(APIView):
    """
    Покупка подписки (заглушка для будущей реализации).

    PURPOSE:
      - Обрабатывает запросы на покупку подписки
      - Интегрируется с платежными системами
      - Создает транзакции и активирует подписки
    """
    permission_classes = [IsAuthenticated]
    serializer_class = PurchaseResponseSerializer
    
    def post(self, request):
        """Инициация покупки подписки."""
        plan_id = request.data.get('plan_id')
        payment_method = request.data.get('payment_method', 'card')
        
        if not plan_id:
            return Response({
                'error': 'plan_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            plan = SubscriptionPlan.objects.get(id=plan_id, is_active=True)
        except SubscriptionPlan.DoesNotExist:
            return Response({
                'error': 'Invalid plan_id'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Здесь должна быть интеграция с платежной системой
        # Пока возвращаем заглушку
        return Response({
            'message': 'Payment integration not implemented yet',
            'plan': {
                'id': str(plan.id),
                'name': plan.name,
                'price': str(plan.price),
                'currency': plan.currency
            },
            'next_steps': 'Integration with payment provider required'
        }, status=status.HTTP_501_NOT_IMPLEMENTED)


@extend_schema(
    operation_id='activate_subscription',
    summary='Активация подписки через промокод',
    description="""
    Активирует платную подписку с помощью промокода.

    Эндпоинт доступен для всех аутентифицированных пользователей (анонимных и зарегистрированных).
    При активации:
    - Деактивируется текущая активная подписка пользователя
    - Создается новая подписка согласно тарифному плану промокода
    - Обновляются лимиты в Hiddify Manager
    - Промокод помечается как использованный
    """,
    tags=['Subscriptions'],
    request=SubscriptionActivationSerializer,
    responses={
        200: SubscriptionActivationResponseSerializer,
        400: SubscriptionErrorResponseSerializer,
        403: SubscriptionErrorResponseSerializer,
        404: SubscriptionErrorResponseSerializer,
        500: SubscriptionErrorResponseSerializer,
    },
    examples=[
        OpenApiExample(
            'Успешная активация',
            value={
                'promo_code': 'PREMIUM2024'
            },
            request_only=True
        ),
        OpenApiExample(
            'Успешный ответ',
            value={
                'success': True,
                'message': 'Subscription activated successfully!',
                'subscription': {
                    'plan_name': 'Premium',
                    'end_date': '2024-07-01T12:00:00Z',
                    'traffic_limit_gb': 100,
                    'duration_days': 30,
                    'is_active': True
                }
            },
            response_only=True
        ),
        OpenApiExample(
            'Ошибка - промокод не найден',
            value={
                'error': 'Промокод не найден или недействителен',
                'code': 'INVALID_PROMO_CODE'
            },
            response_only=True,
            status_codes=['404']
        )
    ]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def activate_subscription(request):
    """
    Активирует платную подписку с помощью промокода.

    PURPOSE:
      - Обрабатывает запросы на активацию промокодов
      - Создает новую подписку согласно тарифному плану промокода
      - Обновляет лимиты в Hiddify Manager
      - Деактивирует использованный промокод

    AAG (Actor -> Action -> Goal):
      - Аутентифицированный пользователь -> Отправляет промокод -> Активирует платную подписку
      - Система -> Обрабатывает активацию -> Предоставляет VPN сервис согласно новому тарифу

    CONTRACT:
      PRECONDITIONS:
        - Пользователь должен быть аутентифицирован (JWT токен)
        - request.data содержит promo_code
        - promo_code существует и действителен
      POSTCONDITIONS:
        - Промокод активирован и помечен как использованный
        - Создана новая подписка для пользователя
        - Обновлены лимиты в Hiddify Manager
        - Возвращен ответ с информацией о подписке
      INVARIANTS:
        - Все операции выполняются атомарно
        - При ошибке состояние системы не изменяется
    """
    # Валидация входных данных
    serializer = SubscriptionActivationSerializer(data=request.data)
    if not serializer.is_valid():
        return Response({
            'error': 'Некорректные данные запроса',
            'code': 'VALIDATION_ERROR',
            'details': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    promo_code = serializer.validated_data['promo_code']
    user = request.user

    try:
        with transaction.atomic():
            # Ищем промокод в базе
            try:
                promo_obj = PromoCode.objects.select_related('plan').get(code=promo_code)
            except PromoCode.DoesNotExist:
                logger.warning(f"Promo code not found: {promo_code}")
                return Response({
                    'error': 'Промокод не найден или недействителен',
                    'code': 'INVALID_PROMO_CODE'
                }, status=status.HTTP_404_NOT_FOUND)

            # Проверяем валидность промокода
            if not promo_obj.is_valid():
                if promo_obj.is_activated:
                    error_msg = 'Промокод уже был использован'
                    error_code = 'PROMO_CODE_ALREADY_USED'
                elif promo_obj.is_expired():
                    error_msg = 'Срок действия промокода истек'
                    error_code = 'PROMO_CODE_EXPIRED'
                else:
                    error_msg = 'Промокод недействителен'
                    error_code = 'INVALID_PROMO_CODE'

                logger.warning(f"Invalid promo code {promo_code}: {error_msg}")
                return Response({
                    'error': error_msg,
                    'code': error_code
                }, status=status.HTTP_400_BAD_REQUEST)

            # Получаем тарифный план из промокода
            tariff_plan = promo_obj.plan

            # Деактивируем текущую активную подписку пользователя
            ActiveSubscription.objects.filter(
                user=user,
                is_active=True
            ).update(is_active=False)

            # Создаем новую подписку
            start_date = timezone.now()
            end_date = start_date + timedelta(days=tariff_plan.duration_days)

            new_subscription = ActiveSubscription.objects.create(
                user=user,
                plan=tariff_plan,
                start_date=start_date,
                end_date=end_date,
                is_active=True,
                payment_method='promo_code'
            )

            # Привязываем подписку к устройству, с которого пришел запрос
            try:
                # Извлекаем ID устройства из токена, которым подписан запрос
                device_model_id = request.auth.payload.get('deviceId')
                if device_model_id:
                    device = UserDevice.objects.get(id=device_model_id)
                    SubscriptionDevice.objects.create(subscription=new_subscription, device=device)
                    logger.info(f"Linked subscription {new_subscription.id} to device {device.device_id}")
            except (AttributeError, KeyError, UserDevice.DoesNotExist) as e:
                # Логируем, что не смогли привязать подписку к устройству,
                # но не прерываем операцию, так как активация подписки важнее.
                logger.warning(f"Could not link new subscription {new_subscription.id} to a device for user {user.id}: {str(e)}")

            # Обновляем данные в Hiddify Manager
            hiddify_service = HiddifyApiService()

            try:
                hiddify_link = user.hiddify_link

                # Обновляем пользователя в Hiddify
                update_data = {
                    'is_active': True,
                    'usage_limit_GB': tariff_plan.traffic_limit_gb,
                    'package_days': tariff_plan.duration_days
                }

                success, response = hiddify_service.update_hiddify_user(
                    str(hiddify_link.hiddify_user_uuid),
                    update_data
                )

                if success:
                    # Обновляем локальные данные
                    hiddify_link.is_active_in_hiddify = True
                    hiddify_link.traffic_limit_bytes = tariff_plan.traffic_limit_gb * 1024 * 1024 * 1024
                    hiddify_link.save()
                    logger.info(f"Updated Hiddify limits for user {user.id}")
                else:
                    logger.error(f"Failed to update Hiddify user {hiddify_link.hiddify_user_uuid}: {response}")
                    return Response({
                        'error': 'Не удалось обновить VPN-доступ. Попробуйте позже.',
                        'code': 'HIDDIFY_UPDATE_FAILED'
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            except Exception as e:
                logger.error(f"Error updating Hiddify for user {user.id}: {str(e)}")
                return Response({
                    'error': 'Ошибка обновления VPN-доступа',
                    'code': 'HIDDIFY_UPDATE_FAILED'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Активируем промокод (помечаем как использованный)
            promo_obj.activate(user)

            logger.info(f"Successfully activated promo code {promo_code} for user {user.id}")

            return Response({
                'success': True,
                'message': 'Subscription activated successfully!',
                'subscription': {
                    'plan_name': tariff_plan.name,
                    'end_date': new_subscription.end_date.isoformat(),
                    'traffic_limit_gb': tariff_plan.traffic_limit_gb,
                    'duration_days': tariff_plan.duration_days,
                    'is_active': True
                }
            }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error activating promo code {promo_code} for user {user.id}: {str(e)}")
        return Response({
            'error': 'Ошибка активации подписки. Попробуйте позже.',
            'code': 'ACTIVATION_FAILED'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
