"""
Views for subscriptions app.
"""
from rest_framework import generics, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from django.utils import timezone
from drf_spectacular.utils import extend_schema, OpenApiExample
from .models import SubscriptionPlan, ActiveSubscription
from .serializers import (
    SubscriptionPlanSerializer,
    ActiveSubscriptionSerializer,
    PurchaseRequestSerializer,
    PurchaseResponseSerializer,
    CurrentSubscriptionResponseSerializer
)


@extend_schema(tags=['Subscriptions'])
class SubscriptionPlanListView(generics.ListAPIView):
    """
    Список доступных тарифных планов.
    
    PURPOSE:
      - Отображает все активные тарифные планы
      - Позволяет пользователям выбрать подходящий тариф
      - Предоставляет информацию о ценах и лимитах
    """
    queryset = SubscriptionPlan.objects.filter(is_active=True)
    serializer_class = SubscriptionPlanSerializer
    permission_classes = []  # Публичный доступ для просмотра планов


@extend_schema(
    tags=['Subscriptions'],
    responses={
        200: CurrentSubscriptionResponseSerializer,
        404: OpenApiExample(
            'No subscription',
            value={'error': 'No active subscription found'}
        )
    }
)
class CurrentSubscriptionView(APIView):
    """
    Информация о текущей подписке пользователя.

    PURPOSE:
      - Отображает активную подписку пользователя
      - Показывает статистику использования
      - Предоставляет информацию о сроках действия
    """
    permission_classes = [IsAuthenticated]
    serializer_class = CurrentSubscriptionResponseSerializer
    
    def get(self, request):
        """Получение информации о текущей подписке."""
        try:
            subscription = ActiveSubscription.objects.get(
                user=request.user,
                is_active=True,
                start_date__lte=timezone.now(),
                end_date__gte=timezone.now()
            )
            
            # Получаем информацию о трафике из Hiddify Link
            traffic_info = {}
            try:
                hiddify_link = request.user.hiddify_link
                traffic_info = {
                    'traffic_used_gb': round(hiddify_link.traffic_used_bytes / (1024**3), 2) if hiddify_link.traffic_used_bytes else 0,
                    'traffic_limit_gb': subscription.plan.traffic_limit_gb,
                    'last_sync': hiddify_link.last_traffic_sync.isoformat() if hiddify_link.last_traffic_sync else None
                }
            except:
                pass
            
            serializer = ActiveSubscriptionSerializer(subscription)
            response_data = serializer.data
            response_data['traffic_info'] = traffic_info
            
            return Response(response_data)
            
        except ActiveSubscription.DoesNotExist:
            return Response({
                'error': 'No active subscription found'
            }, status=status.HTTP_404_NOT_FOUND)


@extend_schema(
    tags=['Subscriptions'],
    request=PurchaseRequestSerializer,
    responses={
        200: PurchaseResponseSerializer,
        400: OpenApiExample(
            'Invalid request',
            value={'error': 'Invalid plan_id or payment_method'}
        )
    }
)
class PurchaseSubscriptionView(APIView):
    """
    Покупка подписки (заглушка для будущей реализации).

    PURPOSE:
      - Обрабатывает запросы на покупку подписки
      - Интегрируется с платежными системами
      - Создает транзакции и активирует подписки
    """
    permission_classes = [IsAuthenticated]
    serializer_class = PurchaseResponseSerializer
    
    def post(self, request):
        """Инициация покупки подписки."""
        plan_id = request.data.get('plan_id')
        payment_method = request.data.get('payment_method', 'card')
        
        if not plan_id:
            return Response({
                'error': 'plan_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            plan = SubscriptionPlan.objects.get(id=plan_id, is_active=True)
        except SubscriptionPlan.DoesNotExist:
            return Response({
                'error': 'Invalid plan_id'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Здесь должна быть интеграция с платежной системой
        # Пока возвращаем заглушку
        return Response({
            'message': 'Payment integration not implemented yet',
            'plan': {
                'id': str(plan.id),
                'name': plan.name,
                'price': str(plan.price),
                'currency': plan.currency
            },
            'next_steps': 'Integration with payment provider required'
        }, status=status.HTTP_501_NOT_IMPLEMENTED)
