"""
Admin configuration for accounts app.
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.html import format_html
from .models import UserAccount, UserDevice, HiddifyLink, ActivationCode


class UserDeviceInline(admin.TabularInline):
    """
    Inline для отображения устройств пользователя.

    PURPOSE:
      - Обеспечивает быстрый просмотр всех устройств пользователя
      - Позволяет управлять устройствами прямо из профиля пользователя
      - Упрощает администрирование связанных данных

    AAG (Actor -> Action -> Goal):
      - Администратор -> Просматривает устройства пользователя -> Контролирует доступ и активность
    """
    model = UserDevice
    extra = 0
    fields = ('device_name', 'device_type', 'is_active', 'last_seen')
    readonly_fields = ('last_seen',)
    can_delete = True


class ActiveSubscriptionInline(admin.TabularInline):
    """
    Inline для отображения активных подписок пользователя.

    PURPOSE:
      - Показывает текущие подписки пользователя
      - Обеспечивает быстрый доступ к информации о подписках
      - Упрощает управление подписками из профиля пользователя

    AAG (Actor -> Action -> Goal):
      - Администратор -> Просматривает подписки пользователя -> Контролирует доступ к сервису
    """
    from subscriptions.models import ActiveSubscription
    model = ActiveSubscription
    extra = 0
    fields = ('plan', 'start_date', 'end_date', 'is_active')
    readonly_fields = ('start_date', 'end_date')
    can_delete = False


class HiddifyLinkInline(admin.TabularInline):
    """
    Inline для отображения связей с Hiddify Manager.

    PURPOSE:
      - Показывает связи пользователя с Hiddify Manager
      - Обеспечивает контроль интеграции с VPN инфраструктурой
      - Упрощает диагностику проблем с VPN доступом

    AAG (Actor -> Action -> Goal):
      - Администратор -> Просматривает Hiddify связи -> Диагностирует VPN проблемы
    """
    model = HiddifyLink
    extra = 0
    fields = ('hiddify_user_uuid', 'is_active_in_hiddify', 'traffic_used_gb')
    readonly_fields = ('hiddify_user_uuid', 'traffic_used_gb')
    can_delete = False

    def traffic_used_gb(self, obj):
        """Display traffic usage in GB."""
        if obj.traffic_used_bytes:
            return f"{obj.traffic_used_bytes / (1024**3):.2f} GB"
        return "0 GB"
    traffic_used_gb.short_description = "Traffic Used"


@admin.register(UserAccount)
class UserAccountAdmin(UserAdmin):
    """
    Административная панель для управления пользователями VPN-сервиса.

    PURPOSE:
      - Обеспечивает полное управление пользователями включая анонимных
      - Предоставляет быстрый доступ к связанным данным (устройства, подписки)
      - Включает кастомные действия для массового управления
      - Отображает ключевую информацию о подписках и активности

    AAG (Actor -> Action -> Goal):
      - Администратор -> Управляет пользователями -> Контролирует доступ к VPN-сервису
      - Поддержка -> Помогает пользователям -> Решает проблемы с аккаунтами
    """

    list_display = ('email', 'id', 'is_anonymous', 'is_active', 'get_active_subscription_plan', 'date_joined')
    list_filter = ('is_anonymous', 'is_active', 'is_staff', 'date_joined')
    search_fields = ('email', 'id')
    readonly_fields = ('id', 'date_joined', 'last_login', 'get_active_subscription_plan')
    ordering = ('-date_joined',)
    inlines = [UserDeviceInline, ActiveSubscriptionInline, HiddifyLinkInline]

    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        ('Personal info', {'fields': ('first_name', 'last_name', 'phone')}),
        ('Account Type', {'fields': ('is_anonymous', 'is_email_verified')}),
        ('Permissions', {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
        ('VPN Service', {'fields': ('last_login_ip', 'get_active_subscription_plan')}),
        ('System', {'fields': ('id',)}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'password1', 'password2', 'is_anonymous'),
        }),
    )

    actions = ['activate_users', 'deactivate_users']

    def get_active_subscription_plan(self, obj):
        """
        Отображает информацию об активной подписке пользователя.

        PURPOSE:
          - Показывает название плана и дату окончания активной подписки
          - Обеспечивает быстрый доступ к информации о подписке
          - Помогает в диагностике проблем с доступом

        ARGS:
          - obj (UserAccount): Объект пользователя

        RETURNS:
          - str: Информация о подписке или "No active subscription"
        """
        try:
            from subscriptions.models import ActiveSubscription
            subscription = ActiveSubscription.objects.filter(
                user=obj,
                is_active=True
            ).select_related('plan').first()

            if subscription:
                return f"{subscription.plan.name} (до {subscription.end_date.strftime('%d.%m.%Y')})"
            return "No active subscription"
        except Exception:
            return "Error loading subscription"

    get_active_subscription_plan.short_description = "Active Subscription"

    def activate_users(self, request, queryset):
        """
        Массовая активация выбранных пользователей.

        PURPOSE:
          - Позволяет быстро активировать множество пользователей
          - Упрощает массовое управление аккаунтами
          - Обеспечивает эффективное администрирование

        ARGS:
          - request: HTTP запрос
          - queryset: QuerySet выбранных пользователей
        """
        updated = queryset.update(is_active=True)
        self.message_user(
            request,
            f"Successfully activated {updated} user(s)."
        )

    activate_users.short_description = "Activate selected users"

    def deactivate_users(self, request, queryset):
        """
        Массовая деактивация выбранных пользователей.

        PURPOSE:
          - Позволяет быстро деактивировать множество пользователей
          - Упрощает блокировку нарушителей или неактивных аккаунтов
          - Обеспечивает эффективное администрирование

        ARGS:
          - request: HTTP запрос
          - queryset: QuerySet выбранных пользователей
        """
        updated = queryset.update(is_active=False)
        self.message_user(
            request,
            f"Successfully deactivated {updated} user(s)."
        )

    deactivate_users.short_description = "Deactivate selected users"


@admin.register(ActivationCode)
class ActivationCodeAdmin(admin.ModelAdmin):
    """
    Административная панель для управления кодами активации.

    PURPOSE:
      - Обеспечивает управление кодами активации для двухуровневой аутентификации
      - Предоставляет информацию об использовании кодов
      - Помогает в диагностике проблем с активацией устройств

    AAG (Actor -> Action -> Goal):
      - Администратор -> Просматривает коды активации -> Контролирует безопасность переноса устройств
      - Поддержка -> Помогает с активацией -> Решает проблемы пользователей
    """

    list_display = ('code', 'user_email', 'is_active', 'is_expired_display', 'used_at', 'created_at')
    list_filter = ('is_active', 'created_at', 'expires_at')
    search_fields = ('code', 'user__email', 'used_by_device_id')
    readonly_fields = ('code', 'created_at', 'expires_at', 'used_at', 'used_by_device_id')
    ordering = ('-created_at',)

    fieldsets = (
        ('Code Information', {
            'fields': ('code', 'user', 'created_at', 'expires_at')
        }),
        ('Status', {
            'fields': ('is_active', 'used_at', 'used_by_device_id')
        }),
    )

    def user_email(self, obj):
        """Отображает email пользователя или Anonymous для анонимных."""
        if obj.user.is_anonymous:
            return f"Anonymous {str(obj.user.id)[:8]}"
        return obj.user.email or f"User {str(obj.user.id)[:8]}"

    user_email.short_description = "User"
    user_email.admin_order_field = 'user__email'

    def is_expired_display(self, obj):
        """Отображает статус истечения кода."""
        if obj.is_expired():
            return format_html('<span style="color: red;">Expired</span>')
        return format_html('<span style="color: green;">Valid</span>')

    is_expired_display.short_description = "Status"

    def has_add_permission(self, request):
        """Запрещаем создание кодов через админку - только через API."""
        return False

    def has_change_permission(self, request, obj=None):
        """Запрещаем изменение кодов через админку."""
        return False


@admin.register(UserDevice)
class UserDeviceAdmin(admin.ModelAdmin):
    """
    Административная панель для управления устройствами пользователей.

    PURPOSE:
      - Обеспечивает управление устройствами пользователей
      - Предоставляет информацию об активности устройств
      - Помогает в диагностике проблем с подключением

    AAG (Actor -> Action -> Goal):
      - Администратор -> Управляет устройствами -> Контролирует доступ к VPN
      - Поддержка -> Диагностирует проблемы -> Помогает пользователям с устройствами
    """

    list_display = ('user_display', 'device_name', 'device_type', 'is_active', 'last_seen', 'created_at')
    list_filter = ('device_type', 'is_active', 'created_at')
    search_fields = ('user__email', 'device_name', 'device_id')
    ordering = ('-created_at',)

    fieldsets = (
        (None, {'fields': ('user', 'device_id', 'device_name', 'device_type')}),
        ('Status', {'fields': ('is_active', 'last_seen')}),
        ('Notifications', {'fields': ('fcm_token',)}),
    )

    def user_display(self, obj):
        """Отображает пользователя с учетом анонимных."""
        if obj.user.is_anonymous:
            return f"Anonymous {str(obj.user.id)[:8]}"
        return obj.user.email or f"User {str(obj.user.id)[:8]}"

    user_display.short_description = "User"
    user_display.admin_order_field = 'user__email'


@admin.register(HiddifyLink)
class HiddifyLinkAdmin(admin.ModelAdmin):
    """
    Административная панель для управления связями с Hiddify Manager.

    PURPOSE:
      - Обеспечивает управление интеграцией с Hiddify Manager
      - Предоставляет информацию о трафике и статусе VPN
      - Помогает в диагностике проблем с VPN подключением

    AAG (Actor -> Action -> Goal):
      - Администратор -> Управляет Hiddify связями -> Контролирует VPN инфраструктуру
      - Поддержка -> Диагностирует VPN проблемы -> Помогает пользователям с подключением
    """

    list_display = ('user_display', 'hiddify_user_uuid', 'is_active_in_hiddify', 'traffic_used_gb', 'last_traffic_sync')
    list_filter = ('is_active_in_hiddify', 'created_at')
    search_fields = ('user__email', 'hiddify_user_uuid')
    ordering = ('-created_at',)
    readonly_fields = ('hiddify_user_uuid', 'hiddify_created_at', 'created_at', 'updated_at')

    fieldsets = (
        (None, {'fields': ('user', 'device', 'hiddify_user_uuid')}),
        ('Hiddify Status', {'fields': ('is_active_in_hiddify', 'hiddify_created_at')}),
        ('Traffic Statistics', {'fields': ('traffic_used_bytes', 'traffic_limit_bytes', 'last_traffic_sync')}),
        ('Metadata', {'fields': ('hiddify_comment', 'last_config_request')}),
        ('Timestamps', {'fields': ('created_at', 'updated_at')}),
    )

    def user_display(self, obj):
        """Отображает пользователя с учетом анонимных."""
        if obj.user.is_anonymous:
            return f"Anonymous {str(obj.user.id)[:8]}"
        return obj.user.email or f"User {str(obj.user.id)[:8]}"

    user_display.short_description = "User"
    user_display.admin_order_field = 'user__email'

    def traffic_used_gb(self, obj):
        """Display traffic usage in GB."""
        if obj.traffic_used_bytes:
            return f"{obj.traffic_used_bytes / (1024**3):.2f} GB"
        return "0 GB"
    traffic_used_gb.short_description = "Traffic Used"
