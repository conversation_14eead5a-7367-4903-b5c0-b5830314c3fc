"""
Views for accounts app.
"""
from rest_framework import status, generics
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from django.db import transaction
from django.utils import timezone
import logging
import json
from datetime import datetime, timedelta
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
from drf_spectacular.types import OpenApiTypes

from .models import UserAccount, UserDevice, HiddifyLink, ActivationCode, UserSession
from .serializers import (
    UserRegistrationSerializer,
    UserLoginSerializer,
    UserProfileSerializer,
    UserDeviceSerializer,
    RegistrationResponseSerializer,
    LoginResponseSerializer,
    ErrorResponseSerializer,
    ActivationCodeGenerateSerializer,
    ActivationCodeResponseSerializer,
    DeviceActivationSerializer,
    DeviceActivationResponseSerializer,
    DeviceInitSerializer,
    DeviceInitResponseSerializer,
    ConvertAnonymousSerializer,
    ConvertAnonymousResponseSerializer,
    DeviceRegisterSerializer,
    DeviceRegisterResponseSerializer
)
from subscriptions.models import SubscriptionPlan, ActiveSubscription, SubscriptionDevice
from vpn.services import HiddifyApiService

logger = logging.getLogger(__name__)


@extend_schema(
    tags=['Authentication'],
    summary='Register new user or convert anonymous user',
    description='''
    Supports two scenarios:
    1. **New User Registration** (unauthenticated request): Creates a completely new user account
    2. **Anonymous User Conversion** (authenticated anonymous user): Converts existing anonymous user to registered

    The endpoint automatically detects the scenario based on authentication status.
    ''',
    request=UserRegistrationSerializer,
    responses={
        201: RegistrationResponseSerializer,  # New user created
        200: RegistrationResponseSerializer,  # Anonymous user converted
        400: ErrorResponseSerializer,
        409: ErrorResponseSerializer,
        500: ErrorResponseSerializer
    },
    examples=[
        OpenApiExample(
            'New User Registration',
            description='Unauthenticated request to create new user',
            value={
                'email': '<EMAIL>',
                'password': 'securepassword123',
                'password_confirm': 'securepassword123',
                'device_id': 'unique-device-id-123',
                'device_name': 'iPhone 13',
                'device_type': 'ios'
            }
        ),
        OpenApiExample(
            'Anonymous User Conversion',
            description='Authenticated anonymous user converting to registered',
            value={
                'email': '<EMAIL>',
                'password': 'securepassword123',
                'password_confirm': 'securepassword123'
            }
        )
    ]
)
@api_view(['POST'])
@permission_classes([AllowAny])
def register_user(request):
    """
    Регистрация нового пользователя или конвертация анонимного пользователя.

    PURPOSE:
      - Поддерживает два сценария: новая регистрация и конвертация анонимного пользователя
      - Автоматически определяет сценарий по статусу аутентификации
      - Создает или обновляет VPN-доступ в Hiddify
      - Возвращает JWT токены для дальнейшей аутентификации

    SCENARIOS:
      1. Неаутентифицированный запрос: Создание нового пользователя
      2. Аутентифицированный анонимный пользователь: Конвертация в зарегистрированного

    AAG (Actor -> Action -> Goal):
      - Новый пользователь -> Регистрируется -> Получает VPN-доступ
      - Анонимный пользователь -> Конвертируется -> Сохраняет VPN-доступ и получает полноценный аккаунт

    CONTRACT:
      PRECONDITIONS:
        - email (str): Валидный email адрес
        - password (str): Пароль минимум 8 символов
        - device_id (str): Требуется только для новых пользователей
      POSTCONDITIONS:
        - Создается или обновляется UserAccount
        - Обновляется информация в Hiddify
        - Возвращаются JWT токены
      INVARIANTS:
        - email уникален в системе
        - При ошибке все изменения откатываются
    """
    # Определяем сценарий по аутентификации и статусу пользователя
    is_anonymous_conversion = (
        request.user.is_authenticated and
        hasattr(request.user, 'is_anonymous') and
        request.user.is_anonymous
    )

    # Инициализируем сериализатор с контекстом для определения сценария
    serializer = UserRegistrationSerializer(data=request.data, context={'request': request})

    if not serializer.is_valid():
        # Проверяем специальную ошибку для 409 Conflict
        if any('_email_exists' in str(error) for error in serializer.errors.values()):
            return Response(
                {"email": ["User with this email already exists."]},
                status=status.HTTP_409_CONFLICT
            )

        # Все остальные ошибки валидации - 400 Bad Request
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    try:
        if is_anonymous_conversion:
            # Сценарий 2: Конвертация анонимного пользователя
            with transaction.atomic():
                user_account = request.user

                # Обновляем данные пользователя
                user_account.email = serializer.validated_data['email']
                user_account.username = serializer.validated_data['email']
                user_account.set_password(serializer.validated_data['password'])
                user_account.is_anonymous = False
                user_account.save()

                # Получаем существующее устройство пользователя
                user_device = user_account.devices.filter(is_active=True).first()
                if not user_device:
                    raise Exception("Anonymous user has no active devices")

                # Деактивируем старые сессии перед созданием новых
                UserSession.objects.filter(user=user_account).update(is_active=False)

                # Обновляем данные в Hiddify Manager
                hiddify_service = HiddifyApiService()

                try:
                    hiddify_link = user_account.hiddify_link

                    # Обновляем метаданные для Hiddify
                    updated_comment_data = {
                        'user_id': str(user_account.id),
                        'device_id': user_device.device_id,
                        'email': user_account.email,
                        'is_anonymous': False,
                        'converted_at': datetime.now().isoformat()
                    }

                    # Обновляем пользователя в Hiddify (меняем имя с anon_ на user_)
                    update_data = {
                        'name': f"user_{str(user_account.id)[:8]}",
                        'comment': json.dumps(updated_comment_data)
                    }

                    success, hiddify_response = hiddify_service.update_hiddify_user(
                        str(hiddify_link.hiddify_user_uuid),
                        update_data
                    )

                    if success:
                        # Обновляем локальные метаданные
                        hiddify_link.hiddify_comment = updated_comment_data
                        hiddify_link.save()
                        logger.info(f"Updated Hiddify user for conversion: {hiddify_link.hiddify_user_uuid}")
                    else:
                        logger.warning(f"Failed to update Hiddify user during conversion: {hiddify_response}")

                except HiddifyLink.DoesNotExist:
                    logger.error(f"Anonymous user {user_account.id} has no Hiddify link")
                    raise Exception("Anonymous user has no VPN access configured")

                # Генерируем новые JWT токены с правильным типом
                refresh = RefreshToken.for_user(user_account)
                access_token = refresh.access_token

                # Добавляем claims для зарегистрированного пользователя
                access_token['userId'] = str(user_account.id)
                access_token['deviceId'] = str(user_device.id)
                access_token['tokenType'] = 'registered'
                access_token['hiddify_uuid'] = str(hiddify_link.hiddify_user_uuid)

                # Создаем новую сессию
                UserSession.objects.create(
                    user=user_account,
                    device=user_device,
                    refresh_token=str(refresh),
                    expires_at=timezone.now() + timedelta(days=60),
                    is_active=True
                )

                tokens = {
                    'access': str(access_token),
                    'refresh': str(refresh)
                }

                # Получаем информацию о подписке (используем ActiveSubscription)
                active_subscription = ActiveSubscription.objects.select_related('plan').filter(
                    user=user_account,
                    is_active=True
                ).first()

                logger.info(f"Successfully converted anonymous user {user_account.id} to registered user {user_account.email}")

                return Response({
                    'success': True,
                    'user': {
                        'id': str(user_account.id),
                        'email': user_account.email,
                        'created_at': user_account.created_at.isoformat()
                    },
                    'subscription': {
                        'plan_name': active_subscription.plan.name if active_subscription else 'No active subscription',
                        'end_date': active_subscription.end_date.isoformat() if active_subscription else None,
                        'traffic_limit_gb': active_subscription.plan.traffic_limit_gb if active_subscription else 0
                    },
                    'tokens': tokens
                }, status=status.HTTP_200_OK)

        else:
            # Сценарий 1: Регистрация нового пользователя
            with transaction.atomic():
                # Создаем пользователя через сериализатор
                user_account = serializer.save()
                device_data = user_account._device_data

                # Создаем устройство для нового пользователя
                user_device = UserDevice.objects.create(
                    user=user_account,
                    device_id=device_data['device_id'],
                    device_name=device_data.get('device_name', ''),
                    device_type=device_data.get('device_type', ''),
                    is_active=True
                )

                # Получаем пробный тариф для новых пользователей
                trial_plan = SubscriptionPlan.objects.filter(
                    is_trial=True,
                    is_active=True
                ).first()

                if not trial_plan:
                    # Fallback на самый дешевый активный тариф
                    trial_plan = SubscriptionPlan.objects.filter(
                        is_active=True
                    ).order_by('price').first()

                if not trial_plan:
                    raise Exception("No active subscription plans available")

                # Создаем пользователя в Hiddify Manager
                hiddify_service = HiddifyApiService()

                # Формируем метаданные для Hiddify
                comment_data = {
                    'user_id': str(user_account.id),
                    'device_id': device_data['device_id'],
                    'plan_id': str(trial_plan.id),
                    'email': user_account.email,
                    'is_anonymous': False,
                    'created_at': datetime.now().isoformat()
                }

                success, hiddify_response = hiddify_service.create_hiddify_user(
                    name=f"user_{str(user_account.id)[:8]}",
                    usage_limit_gb=trial_plan.traffic_limit_gb,
                    package_days=trial_plan.duration_days,
                    comment_json_string=json.dumps(comment_data)
                )

                if not success:
                    raise Exception(f"Failed to create Hiddify user: {hiddify_response}")

                hiddify_user_uuid = hiddify_response['uuid']

                # Создаем связь с Hiddify Manager
                hiddify_link = HiddifyLink.objects.create(
                    user=user_account,
                    device=user_device,
                    hiddify_user_uuid=hiddify_user_uuid,
                    hiddify_comment=comment_data,
                    traffic_limit_bytes=trial_plan.traffic_limit_gb * 1024 * 1024 * 1024,
                    is_active_in_hiddify=True,
                    hiddify_created_at=timezone.now()
                )

                # Создаем активную подписку
                subscription = ActiveSubscription.objects.create(
                    user=user_account,
                    plan=trial_plan,
                    start_date=timezone.now(),
                    end_date=timezone.now() + timedelta(days=trial_plan.duration_days),
                    is_active=True
                )

                # Генерируем JWT токены для зарегистрированного пользователя
                refresh = RefreshToken.for_user(user_account)
                access_token = refresh.access_token

                # Добавляем claims в токен
                access_token['userId'] = str(user_account.id)
                access_token['deviceId'] = str(user_device.id)
                access_token['tokenType'] = 'registered'
                access_token['hiddify_uuid'] = str(hiddify_user_uuid)

                # Создаем сессию пользователя
                UserSession.objects.create(
                    user=user_account,
                    device=user_device,
                    refresh_token=str(refresh),
                    expires_at=timezone.now() + timedelta(days=60),
                    is_active=True
                )

                tokens = {
                    'access': str(access_token),
                    'refresh': str(refresh)
                }

                logger.info(f"Successfully created new user {user_account.email} with Hiddify UUID {hiddify_user_uuid}")

                return Response({
                    'success': True,
                    'user': {
                        'id': str(user_account.id),
                        'email': user_account.email,
                        'created_at': user_account.created_at.isoformat()
                    },
                    'subscription': {
                        'plan_name': trial_plan.name,
                        'end_date': subscription.end_date.isoformat(),
                        'traffic_limit_gb': trial_plan.traffic_limit_gb
                    },
                    'tokens': tokens
                }, status=status.HTTP_201_CREATED)

    except Exception as e:
        operation = "conversion" if is_anonymous_conversion else "registration"
        email = serializer.validated_data.get('email', 'unknown') if serializer.is_valid() else 'unknown'
        logger.error(f"User {operation} failed for {email}: {str(e)}", exc_info=True)

        return Response({
            'error': f'User {operation} failed. Please try again.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    tags=['Authentication'],
    summary='Login user',
    description='Authenticate user and return tokens',
    request=UserLoginSerializer,
    responses={
        200: LoginResponseSerializer,
        400: ErrorResponseSerializer
    },
    examples=[
        OpenApiExample(
            'Login Example',
            value={
                'email': '<EMAIL>',
                'password': 'securepassword123',
                'device_id': 'unique-device-id-123'
            }
        )
    ]
)
@api_view(['POST'])
@permission_classes([AllowAny])
def login_user(request):
    """
    Аутентификация пользователя.
    
    PURPOSE:
      - Проверяет учетные данные пользователя
      - Генерирует JWT токены для доступа
      - Обновляет информацию о последнем входе
    """
    serializer = UserLoginSerializer(data=request.data)
    
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    user = serializer.validated_data['user']
    device_id = serializer.validated_data.get('device_id')
    
    # Обновляем информацию о входе
    user.last_login = timezone.now()
    user.last_login_ip = request.META.get('REMOTE_ADDR')
    user.save(update_fields=['last_login', 'last_login_ip'])
    
    # Обновляем информацию об устройстве, если предоставлен device_id
    if device_id:
        UserDevice.objects.filter(
            user=user,
            device_id=device_id
        ).update(last_seen=timezone.now())
    
    # Генерируем токены
    refresh = RefreshToken.for_user(user)
    access_token = refresh.access_token
    
    # Добавляем дополнительные claims
    if device_id:
        access_token['device_id'] = device_id
    
    # Получаем UUID Hiddify пользователя
    try:
        hiddify_link = user.hiddify_link
        access_token['hiddify_uuid'] = str(hiddify_link.hiddify_user_uuid)
    except HiddifyLink.DoesNotExist:
        pass
    
    return Response({
        'success': True,
        'user': {
            'id': str(user.id),
            'email': user.email,
        },
        'tokens': {
            'access': str(access_token),
            'refresh': str(refresh)
        }
    }, status=status.HTTP_200_OK)


@extend_schema(tags=['User Management'])
class UserProfileView(generics.RetrieveUpdateAPIView):
    """
    Просмотр и обновление профиля пользователя.
    
    PURPOSE:
      - Позволяет пользователю просматривать свой профиль
      - Обеспечивает обновление определенных полей профиля
      - Возвращает статистику использования
    """
    serializer_class = UserProfileSerializer
    permission_classes = [IsAuthenticated]
    
    def get_object(self):
        return self.request.user


@extend_schema(tags=['User Management'])
class UserDeviceListView(generics.ListCreateAPIView):
    """
    Список устройств пользователя и добавление новых устройств.
    
    PURPOSE:
      - Отображает все устройства пользователя
      - Позволяет добавлять новые устройства
      - Контролирует лимиты устройств
    """
    serializer_class = UserDeviceSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return UserDevice.objects.filter(user=self.request.user, is_active=True)
    
    def perform_create(self, serializer):
        serializer.save(user=self.request.user)


@extend_schema(tags=['User Management'])
class UserDeviceDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Детальная информация об устройстве пользователя.

    PURPOSE:
      - Позволяет просматривать информацию об устройстве
      - Обеспечивает обновление данных устройства
      - Позволяет деактивировать устройство
    """
    serializer_class = UserDeviceSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return UserDevice.objects.filter(user=self.request.user)

    def destroy(self, request, *args, **kwargs):
        """
        Мягкое удаление устройства - деактивация вместо физического удаления.

        PURPOSE:
          - Деактивирует устройство пользователя
          - Сохраняет историю устройств для аналитики
          - Возвращает корректный HTTP статус 204
        """
        instance = self.get_object()
        instance.is_active = False
        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)


# Activation Code Views

@extend_schema(
    tags=['Authentication'],
    summary='Generate activation code',
    description='Generate activation code for device transfer',
    request=ActivationCodeGenerateSerializer,
    responses={
        200: ActivationCodeResponseSerializer,
        401: ErrorResponseSerializer,
        500: ErrorResponseSerializer
    },
    examples=[
        OpenApiExample(
            'Success Response',
            description='Успешная генерация кода активации',
            value={
                "activation_code": "A1B2C3D4",
                "expires_at": "2025-06-01T12:15:00Z",
                "expires_in_minutes": 15
            }
        )
    ]
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def generate_activation_code(request):
    """
    Генерирует код активации для переноса VPN доступа на новое устройство.

    PURPOSE:
      - Создает временный код активации для аутентифицированного пользователя
      - Проверяет наличие активной подписки и лимиты устройств
      - Деактивирует предыдущие активные коды пользователя
      - Возвращает код с информацией о времени истечения

    AAG (Actor -> Action -> Goal):
      - Аутентифицированный пользователь -> Запрашивает код -> Получает временный код для нового устройства
      - Система -> Генерирует уникальный код -> Обеспечивает безопасный перенос доступа

    CONTRACT:
      PRECONDITIONS:
        - Пользователь должен быть аутентифицирован (JWT токен)
        - Пользователь должен иметь активную подписку
        - Количество активных устройств не должно превышать лимит тарифа
      POSTCONDITIONS:
        - Создается новый ActivationCode
        - Предыдущие активные коды деактивируются
        - Возвращается код с временем истечения
      INVARIANTS:
        - У пользователя может быть только один активный код
        - Код действителен ровно 10 минут (изменено с 15 на 10 согласно требованиям)
    """
    import random
    import string

    try:
        user = request.user

        # Проверяем наличие активной подписки
        active_subscription = ActiveSubscription.objects.select_related('plan').filter(
            user=user,
            is_active=True,
            start_date__lte=timezone.now(),
            end_date__gte=timezone.now()
        ).first()

        if not active_subscription:
            logger.warning(f"User {user.email} tried to generate activation code without active subscription")
            return Response({
                'error': 'У вас нет активной подписки'
            }, status=status.HTTP_403_FORBIDDEN)

        # Получаем тарифный план и проверяем лимит устройств
        tariff_plan = active_subscription.plan
        current_devices_count = UserDevice.objects.filter(user=user, is_active=True).count()

        if current_devices_count >= tariff_plan.max_devices:
            logger.warning(f"User {user.email} reached device limit ({current_devices_count}/{tariff_plan.max_devices})")
            return Response({
                'error': 'Достигнут лимит устройств для вашего тарифа'
            }, status=status.HTTP_403_FORBIDDEN)

        # Атомарная операция создания кода активации
        with transaction.atomic():
            # Деактивируем все предыдущие активные коды пользователя
            ActivationCode.objects.filter(
                user=user,
                is_active=True
            ).update(is_active=False)

            # Генерируем уникальный 8-символьный код
            while True:
                code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
                if not ActivationCode.objects.filter(code=code).exists():
                    break

            # Устанавливаем время истечения (10 минут от текущего времени)
            expires_at = timezone.now() + timedelta(minutes=10)

            # Создаем код активации
            activation_code = ActivationCode.objects.create(
                code=code,
                user=user,
                expires_at=expires_at,
                is_active=True
            )

        logger.info(f"Generated activation code {code} for user {user.email}")

        return Response({
            'activation_code': activation_code.code,
            'expires_at': activation_code.expires_at.isoformat(),
            'expires_in_minutes': 10
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error generating activation code for user {request.user.email}: {str(e)}")
        return Response({
            'error': 'Failed to generate activation code. Please try again.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    tags=['Authentication'],
    summary='Activate device',
    description='Activate device with activation code',
    request=DeviceActivationSerializer,
    responses={
        200: DeviceActivationResponseSerializer,
        400: ErrorResponseSerializer,
        404: ErrorResponseSerializer,
        410: ErrorResponseSerializer,  # Gone - код истек
        500: ErrorResponseSerializer
    },
    examples=[
        OpenApiExample(
            'Activation Request',
            description='Запрос активации нового устройства',
            value={
                "activation_code": "A1B2C3D4",
                "device_id": "new-device-12345",
                "device_name": "iPad Pro",
                "device_type": "ios"
            }
        ),
        OpenApiExample(
            'Success Response',
            description='Успешная активация устройства',
            value={
                "success": True,
                "message": "Device activated successfully",
                "user": {
                    "id": "123e4567-e89b-12d3-a456-426614174000",
                    "email": "<EMAIL>"
                },
                "tokens": {
                    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
                },
                "device_info": {
                    "device_id": "new-device-12345",
                    "device_name": "iPad Pro",
                    "device_type": "ios"
                }
            }
        )
    ]
)
@api_view(['POST'])
@permission_classes([AllowAny])
def activate_device(request):
    """
    Активирует новое устройство с использованием кода активации.

    PURPOSE:
      - Связывает новое устройство с аккаунтом пользователя через код активации
      - Создает или обновляет запись устройства в системе
      - Генерирует JWT токены для нового устройства
      - Деактивирует использованный код активации

    AAG (Actor -> Action -> Goal):
      - Новое устройство -> Отправляет код активации -> Получает VPN доступ к аккаунту
      - Система -> Проверяет код и связывает устройство -> Обеспечивает безопасную активацию

    CONTRACT:
      PRECONDITIONS:
        - activation_code (str): Валидный 8-символьный код активации
        - device_id (str): Уникальный идентификатор нового устройства
        - Код не должен быть истекшим или использованным
      POSTCONDITIONS:
        - Устройство связывается с пользователем
        - Код активации деактивируется
        - Возвращаются JWT токены для устройства
      INVARIANTS:
        - Код может быть использован только один раз
        - Устройство получает доступ к VPN конфигурациям пользователя
    """
    serializer = DeviceActivationSerializer(data=request.data)

    if not serializer.is_valid():
        return Response({
            'error': 'Validation failed',
            'details': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    activation_code = serializer.validated_data['activation_code']
    device_id = serializer.validated_data['device_id']
    device_name = serializer.validated_data.get('device_name', '')
    device_type = serializer.validated_data.get('device_type', '')

    try:
        # Ищем код активации
        try:
            code_obj = ActivationCode.objects.get(code=activation_code)
        except ActivationCode.DoesNotExist:
            logger.warning(f"Activation code not found: {activation_code}")
            return Response({
                'error': 'Invalid activation code'
            }, status=status.HTTP_404_NOT_FOUND)

        # Проверяем валидность кода
        if not code_obj.is_valid():
            if code_obj.is_expired():
                logger.warning(f"Activation code expired: {activation_code}")
                return Response({
                    'error': 'Activation code has expired'
                }, status=status.HTTP_410_GONE)
            elif code_obj.used_at:
                logger.warning(f"Activation code already used: {activation_code}")
                return Response({
                    'error': 'Activation code has already been used'
                }, status=status.HTTP_400_BAD_REQUEST)
            else:
                logger.warning(f"Activation code inactive: {activation_code}")
                return Response({
                    'error': 'Activation code is not active'
                }, status=status.HTTP_400_BAD_REQUEST)

        user = code_obj.user

        # Проверяем, не привязано ли устройство к другому пользователю
        if UserDevice.objects.filter(device_id=device_id).exclude(user=user).exists():
            logger.warning(f"Device {device_id} is already linked to another user")
            return Response({
                'error': 'This device is already linked to another account.'
            }, status=status.HTTP_409_CONFLICT)

        with transaction.atomic():
            # Получаем активную подписку пользователя для проверки лимита устройств
            active_subscription = ActiveSubscription.objects.select_related('plan').filter(
                user=user,
                is_active=True,
                start_date__lte=timezone.now(),
                end_date__gte=timezone.now()
            ).first()

            max_devices = active_subscription.plan.max_devices if active_subscription else 1

            # Подсчитываем текущие активные устройства
            current_active_devices = UserDevice.objects.filter(user=user, is_active=True).count()

            # Проверяем, существует ли устройство с таким device_id
            existing_device = UserDevice.objects.filter(user=user, device_id=device_id).first()

            if existing_device:
                # Устройство уже существует - просто обновляем и активируем
                existing_device.device_name = device_name or existing_device.device_name
                existing_device.device_type = device_type or existing_device.device_type
                existing_device.is_active = True
                existing_device.last_seen = timezone.now()
                existing_device.save()

                user_device = existing_device
                created = False
                activation_type = "reactivated"
                logger.info(f"Device {device_id} reactivated for user {user.email}")

            elif current_active_devices < max_devices:
                # Можем добавить новое устройство
                user_device = UserDevice.objects.create(
                    user=user,
                    device_id=device_id,
                    device_name=device_name,
                    device_type=device_type,
                    is_active=True,
                    last_seen=timezone.now()
                )
                created = True
                activation_type = "added"
                logger.info(f"Device {device_id} added for user {user.email} ({current_active_devices + 1}/{max_devices})")

            else:
                # Превышен лимит - нужно деактивировать самое старое устройство
                oldest_device = UserDevice.objects.filter(
                    user=user,
                    is_active=True
                ).exclude(
                    device_id=device_id  # Исключаем текущее устройство если оно есть
                ).order_by('last_seen', 'created_at').first()

                if oldest_device:
                    oldest_device.is_active = False
                    oldest_device.save()
                    logger.info(f"Deactivated oldest device {oldest_device.device_id} for user {user.email}")

                # Создаем новое устройство
                user_device = UserDevice.objects.create(
                    user=user,
                    device_id=device_id,
                    device_name=device_name,
                    device_type=device_type,
                    is_active=True,
                    last_seen=timezone.now()
                )
                created = True
                activation_type = "transferred"
                logger.info(f"Device {device_id} transferred for user {user.email} (oldest device deactivated)")

            # Деактивируем код активации
            code_obj.deactivate(device_id=device_id)

            # Генерируем JWT токены для нового устройства
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            # Добавляем дополнительные claims
            access_token['userId'] = str(user.id)
            access_token['deviceId'] = str(user_device.id)  # Используем ID нашей модели, а не от клиента
            access_token['tokenType'] = 'registered' if not user.is_anonymous else 'anonymous'
            access_token['device_id'] = device_id  # Оставляем для обратной совместимости

            # Получаем UUID Hiddify пользователя
            try:
                hiddify_link = user.hiddify_link
                access_token['hiddify_uuid'] = str(hiddify_link.hiddify_user_uuid)
            except HiddifyLink.DoesNotExist:
                logger.warning(f"No Hiddify link found for user {user.email}")

            logger.info(f"Device {device_id} activated for user {user.email} using code {activation_code}")

            # Формируем сообщение в зависимости от типа активации
            if activation_type == "added":
                message = "Device added successfully"
            elif activation_type == "transferred":
                message = "Device transferred successfully. The oldest device has been deactivated."
            elif activation_type == "reactivated":
                message = "Device reactivated successfully"
            else:
                message = "Device activated successfully"

            return Response({
                'success': True,
                'message': message,
                'activation_type': activation_type,
                'user': {
                    'id': str(user.id),
                    'email': user.email
                },
                'tokens': {
                    'access': str(access_token),
                    'refresh': str(refresh)
                },
                'device_info': {
                    'device_id': device_id,
                    'device_name': device_name,
                    'device_type': device_type,
                    'created': created
                }
            }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error activating device {device_id} with code {activation_code}: {str(e)}")
        return Response({
            'error': 'Device activation failed. Please try again.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Anonymous User Views

@extend_schema(
    tags=['Anonymous Users'],
    summary='Initialize device (anonymous user)',
    description='Initialize device and create anonymous user with VPN access',
    request=DeviceInitSerializer,
    responses={
        201: DeviceInitResponseSerializer,
        200: DeviceInitResponseSerializer,
        409: ErrorResponseSerializer,
        400: ErrorResponseSerializer,
        500: ErrorResponseSerializer
    },
    examples=[
        OpenApiExample(
            'Device Initialization',
            value={
                'device_id': 'unique-device-id-123',
                'device_name': 'iPhone 13',
                'device_type': 'ios'
            }
        )
    ]
)
@api_view(['POST'])
@permission_classes([AllowAny])
def init_device(request):
    """
    Инициализация устройства (создание анонимного пользователя).

    PURPOSE:
      - Создает анонимного пользователя для устройства
      - Обеспечивает VPN-доступ без регистрации
      - Поддерживает последующую конвертацию в полноценный аккаунт

    AAG (Actor -> Action -> Goal):
      - Мобильное приложение -> Инициализирует устройство -> Получает VPN-доступ
      - Система -> Создает анонимного пользователя -> Обеспечивает временный доступ

    CONTRACT:
      PRECONDITIONS:
        - device_id (str): Уникальный идентификатор устройства
        - device_name (str): Название устройства
        - device_type (str): Тип устройства
      POSTCONDITIONS:
        - Создается анонимный пользователь (если device_id новый)
        - Создается VPN-доступ через Hiddify
        - Возвращаются JWT токены
      RAISES:
        - 409 Conflict: device_id привязан к зарегистрированному пользователю
        - 400 Bad Request: невалидные данные
        - 500 Internal Server Error: ошибка создания VPN-доступа
    """
    serializer = DeviceInitSerializer(data=request.data)

    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    device_id = serializer.validated_data['device_id']
    device_name = serializer.validated_data['device_name']
    device_type = serializer.validated_data['device_type']

    try:
        # Проверяем, существует ли устройство с таким device_id
        existing_device = UserDevice.objects.filter(device_id=device_id).first()

        if existing_device:
            # Устройство уже существует
            user = existing_device.user

            if not user.is_anonymous:
                # Устройство привязано к зарегистрированному пользователю
                return Response({
                    'error': 'Это устройство уже привязано к зарегистрированному аккаунту. Пожалуйста, войдите в систему.'
                }, status=status.HTTP_409_CONFLICT)

            # Устройство привязано к анонимному пользователю - возвращаем токены
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            # Добавляем дополнительные claims
            access_token['device_id'] = device_id

            # Получаем UUID Hiddify пользователя
            try:
                hiddify_link = user.hiddify_link
                access_token['hiddify_uuid'] = str(hiddify_link.hiddify_user_uuid)
            except HiddifyLink.DoesNotExist:
                pass

            # Получаем активную подписку
            active_subscription = ActiveSubscription.objects.filter(
                user=user,
                is_active=True
            ).first()

            subscription_info = {}
            if active_subscription:
                subscription_info = {
                    'plan_name': active_subscription.plan.name,
                    'end_date': active_subscription.end_date.isoformat(),
                    'traffic_limit_gb': active_subscription.plan.traffic_limit_gb
                }

            return Response({
                'success': True,
                'user': {
                    'id': str(user.id),
                    'is_anonymous': True
                },
                'subscription': subscription_info,
                'tokens': {
                    'access': str(access_token),
                    'refresh': str(refresh)
                }
            }, status=status.HTTP_200_OK)

        # Устройство новое - создаем анонимного пользователя
        user_account = UserAccount.objects.create_user(
            is_anonymous=True
        )

        # Создаем устройство
        user_device = UserDevice.objects.create(
            user=user_account,
            device_id=device_id,
            device_name=device_name,
            device_type=device_type,
            last_seen=timezone.now()
        )

        # Получаем пробный тариф
        trial_plan = SubscriptionPlan.objects.filter(is_trial=True, is_active=True).first()
        if not trial_plan:
            # Fallback на базовый тариф
            trial_plan = SubscriptionPlan.objects.filter(is_active=True).order_by('price').first()

        if not trial_plan:
            raise Exception("No active subscription plans available")

        # Создаем пользователя в Hiddify
        hiddify_service = HiddifyApiService()

        # Формируем метаданные для Hiddify
        comment_data = {
            'user_id': str(user_account.id),
            'device_id': device_id,
            'plan_id': str(trial_plan.id),
            'is_anonymous': True,
            'created_at': datetime.now().isoformat()
        }

        success, hiddify_response = hiddify_service.create_hiddify_user(
            name=f"anon_{str(user_account.id)[:8]}",
            usage_limit_gb=trial_plan.traffic_limit_gb,
            package_days=trial_plan.duration_days,
            comment_json_string=json.dumps(comment_data)
        )

        if not success:
            raise Exception(f"Failed to create Hiddify user: {hiddify_response}")

        hiddify_user_uuid = hiddify_response['uuid']

        # Создаем связь с Hiddify
        HiddifyLink.objects.create(
            user=user_account,
            device=user_device,
            hiddify_user_uuid=hiddify_user_uuid,
            hiddify_comment=comment_data,
            traffic_limit_bytes=trial_plan.traffic_limit_gb * 1024 * 1024 * 1024,
            is_active_in_hiddify=True,
            hiddify_created_at=timezone.now()
        )

        # Создаем подписку
        subscription = ActiveSubscription.objects.create(
            user=user_account,
            plan=trial_plan,
            start_date=timezone.now(),
            end_date=timezone.now() + timedelta(days=trial_plan.duration_days),
            is_active=True
        )

        # Генерируем JWT токены
        refresh = RefreshToken.for_user(user_account)
        access_token = refresh.access_token

        # Добавляем дополнительные claims
        access_token['device_id'] = device_id
        access_token['hiddify_uuid'] = str(hiddify_user_uuid)

        return Response({
            'success': True,
            'user': {
                'id': str(user_account.id),
                'is_anonymous': True
            },
            'subscription': {
                'plan_name': trial_plan.name,
                'end_date': subscription.end_date.isoformat(),
                'traffic_limit_gb': trial_plan.traffic_limit_gb
            },
            'tokens': {
                'access': str(access_token),
                'refresh': str(refresh)
            }
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        logger.error(f"Error initializing device: {str(e)}")
        return Response({
            'error': 'Internal server error during device initialization'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    tags=['Anonymous Users'],
    summary='Convert anonymous account to registered',
    description='Convert anonymous user account to registered account with email and password',
    request=ConvertAnonymousSerializer,
    responses={
        200: ConvertAnonymousResponseSerializer,
        400: ErrorResponseSerializer,
        403: ErrorResponseSerializer,
        500: ErrorResponseSerializer
    },
    examples=[
        OpenApiExample(
            'Convert Anonymous Account',
            value={
                'email': '<EMAIL>',
                'password': 'securepassword123',
                'password_confirm': 'securepassword123'
            }
        )
    ]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def convert_anonymous(request):
    """
    Конвертация анонимного аккаунта в зарегистрированный.

    PURPOSE:
      - Преобразует анонимного пользователя в полноценного зарегистрированного
      - Сохраняет VPN-доступ и историю использования
      - Обеспечивает безопасную конвертацию с проверкой данных

    AAG (Actor -> Action -> Goal):
      - Анонимный пользователь -> Предоставляет email/пароль -> Получает полноценный аккаунт
      - Система -> Конвертирует аккаунт -> Сохраняет VPN-доступ и данные

    CONTRACT:
      PRECONDITIONS:
        - Пользователь должен быть аутентифицирован
        - Пользователь должен иметь is_anonymous=True
        - email (str): Валидный email, не занятый в системе
        - password (str): Пароль, соответствующий требованиям безопасности
      POSTCONDITIONS:
        - Анонимный аккаунт конвертирован в зарегистрированный
        - Сохранены все VPN-данные и подписки
        - Возвращены новые JWT токены
      RAISES:
        - 403 Forbidden: пользователь не анонимный
        - 400 Bad Request: невалидные данные
        - 500 Internal Server Error: ошибка конвертации
    """
    user = request.user

    # Проверяем, что пользователь анонимный
    if not user.is_anonymous:
        return Response({
            'error': 'Этот аккаунт уже зарегистрирован'
        }, status=status.HTTP_403_FORBIDDEN)

    serializer = ConvertAnonymousSerializer(data=request.data)

    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    email = serializer.validated_data['email']
    password = serializer.validated_data['password']

    try:
        # Обновляем пользователя
        user.email = email
        user.set_password(password)
        user.is_anonymous = False
        user.username = email  # Обновляем username для зарегистрированных пользователей
        user.save()

        # Генерируем новые JWT токены
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token

        # Добавляем дополнительные claims
        user_devices = user.devices.filter(is_active=True)
        if user_devices.exists():
            access_token['device_id'] = user_devices.first().device_id

        # Получаем UUID Hiddify пользователя
        try:
            hiddify_link = user.hiddify_link
            access_token['hiddify_uuid'] = str(hiddify_link.hiddify_user_uuid)
        except HiddifyLink.DoesNotExist:
            pass

        logger.info(f"Anonymous user {user.id} converted to registered user {email}")

        return Response({
            'success': True,
            'message': 'Account upgraded successfully.',
            'user': {
                'id': str(user.id),
                'email': user.email
            },
            'tokens': {
                'access': str(access_token),
                'refresh': str(refresh)
            }
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error converting anonymous user {user.id}: {str(e)}")
        return Response({
            'error': 'Internal server error during account conversion'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Device Registration View

def _create_tokens_and_session(user, device):
    """
    Helper-функция для создания JWT токенов и сессии.

    PURPOSE:
      - Убирает дублирование кода между блоками восстановления и новой регистрации
      - Централизует логику создания токенов
      - Обеспечивает единообразную структуру claims

    ARGS:
      - user (UserAccount): Пользователь для которого создаются токены
      - device (UserDevice): Устройство пользователя

    RETURNS:
      - dict: Словарь с access_token и refresh_token
    """
    # Деактивируем все предыдущие сессии для этой пары user и device
    UserSession.objects.filter(user=user, device=device).update(is_active=False)

    # Получаем UUID Hiddify пользователя
    try:
        hiddify_link = user.hiddify_link
        hiddify_uuid = str(hiddify_link.hiddify_user_uuid)
    except HiddifyLink.DoesNotExist:
        hiddify_uuid = None

    # Генерируем новые JWT токены
    refresh = RefreshToken.for_user(user)
    access_token = refresh.access_token

    # Добавляем дополнительные claims
    access_token['userId'] = str(user.id)
    access_token['deviceId'] = str(device.id)
    access_token['tokenType'] = 'anonymous' if user.is_anonymous else 'registered'
    if hiddify_uuid:
        access_token['hiddify_uuid'] = hiddify_uuid

    # Сохраняем refresh токен в базе
    UserSession.objects.create(
        user=user,
        device=device,
        refresh_token=str(refresh),
        expires_at=timezone.now() + timedelta(days=60),
        is_active=True
    )

    return {
        'access_token': str(access_token),
        'refresh_token': str(refresh)
    }


@extend_schema(
    tags=['Device Registration'],
    summary='Register anonymous device',
    description='Register new anonymous device or restore session with device_secret',
    request=DeviceRegisterSerializer,
    responses={
        201: DeviceRegisterResponseSerializer,
        200: DeviceRegisterResponseSerializer,
        400: ErrorResponseSerializer,
        404: ErrorResponseSerializer,
        500: ErrorResponseSerializer
    },
    examples=[
        OpenApiExample(
            'New Device Registration',
            description='Регистрация нового устройства',
            value={
                'device_id': 'unique-device-12345'
            }
        ),
        OpenApiExample(
            'Session Restoration',
            description='Восстановление сессии с device_secret',
            value={
                'device_id': 'unique-device-12345',
                'device_secret': '123e4567-e89b-12d3-a456-426614174000'
            }
        )
    ]
)
@api_view(['POST'])
@permission_classes([AllowAny])
def register_device(request):
    """
    Регистрация нового анонимного устройства или восстановление сессии.

    PURPOSE:
      - Создает новое анонимное устройство с VPN-доступом через Hiddify
      - Восстанавливает сессию по device_secret с актуальной информацией о подписке
      - Генерирует JWT токены с правильными claims
      - Управляет подписками через ActiveSubscription и SubscriptionDevice

    AAG (Actor -> Action -> Goal):
      - Мобильное приложение -> Регистрирует устройство -> Получает VPN-доступ
      - Система -> Создает анонимного пользователя в Django и Hiddify -> Обеспечивает полную интеграцию

    CONTRACT:
      PRECONDITIONS:
        - device_id (str): Идентификатор устройства от клиента
        - device_secret (UUID, optional): Секрет для восстановления сессии
      POSTCONDITIONS:
        - Создается или восстанавливается устройство
        - Возвращаются JWT токены с правильными claims
        - Создается подписка с тарифом по умолчанию и связь через SubscriptionDevice
        - Создается пользователь в Hiddify с HiddifyLink
      RAISES:
        - 201 Created: новое устройство зарегистрировано
        - 200 OK: сессия восстановлена
        - 400 Bad Request: невалидные данные
        - 404 Not Found: device_secret не найден
        - 500 Internal Server Error: ошибка создания VPN-доступа или тарифа по умолчанию
    """
    serializer = DeviceRegisterSerializer(data=request.data)

    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    device_id = serializer.validated_data['device_id']
    device_secret = serializer.validated_data.get('device_secret')

    try:
        # Восстановление сессии
        if device_secret:
            try:
                # Оптимизированный запрос с предзагрузкой связанных моделей
                device = UserDevice.objects.select_related(
                    'user',
                    'user__hiddify_link'
                ).get(device_secret=device_secret)

                # Обновляем device_id если он изменился
                if device.device_id != device_id:
                    device.device_id = device_id
                    device.save(update_fields=['device_id'])

                user = device.user

                # Находим активную подписку пользователя с предзагрузкой плана
                active_subscription = ActiveSubscription.objects.select_related('plan').filter(
                    user=user,
                    is_active=True,
                    start_date__lte=timezone.now(),
                    end_date__gte=timezone.now()
                ).first()

                # Создаем токены и сессию через helper-функцию
                tokens = _create_tokens_and_session(user, device)

                # Формируем информацию о подписке
                subscription_info = {}
                if active_subscription:
                    subscription_info = {
                        'plan_name': active_subscription.plan.name,
                        'expires_at': active_subscription.end_date.isoformat()
                    }

                logger.info(f"Session restored for device {device_id} with secret {device_secret}")

                return Response({
                    **tokens,
                    'subscription': subscription_info
                }, status=status.HTTP_200_OK)

            except UserDevice.DoesNotExist:
                return Response({
                    'error': 'Device secret not found'
                }, status=status.HTTP_404_NOT_FOUND)

        # Новая регистрация
        with transaction.atomic():
            # Получаем тариф по умолчанию
            default_plan = SubscriptionPlan.objects.filter(
                is_free_default=True,
                is_active=True
            ).first()

            if not default_plan:
                logger.error("No default subscription plan found (is_free_default=True)")
                return Response({
                    'error': 'No default subscription plan available'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Создаем анонимного пользователя
            user_account = UserAccount.objects.create_user(is_anonymous=True)

            # Создаем устройство
            user_device = UserDevice.objects.create(
                user=user_account,
                device_id=device_id,
                is_active=True,
                last_seen=timezone.now()
            )

            # Создаем пользователя в Hiddify
            hiddify_service = HiddifyApiService()

            # Формируем метаданные для Hiddify
            comment_data = {
                'user_id': str(user_account.id),
                'device_id': device_id,
                'plan_id': str(default_plan.id),
                'is_anonymous': True,
                'created_at': datetime.now().isoformat()
            }

            success, hiddify_response = hiddify_service.create_hiddify_user(
                name=f"anon_{str(user_account.id)[:8]}",
                usage_limit_gb=default_plan.traffic_limit_gb,
                package_days=default_plan.duration_days,
                comment_json_string=json.dumps(comment_data)
            )

            if not success:
                raise Exception(f"Failed to create Hiddify user: {hiddify_response}")

            hiddify_user_uuid = hiddify_response['uuid']

            # Создаем связь с Hiddify
            hiddify_link = HiddifyLink.objects.create(
                user=user_account,
                device=user_device,
                hiddify_user_uuid=hiddify_user_uuid,
                hiddify_comment=comment_data,
                traffic_limit_bytes=default_plan.traffic_limit_gb * 1024 * 1024 * 1024,
                is_active_in_hiddify=True,
                hiddify_created_at=timezone.now()
            )

            # Создаем подписку (используем ActiveSubscription, так как отдельной модели Subscription нет)
            subscription = ActiveSubscription.objects.create(
                user=user_account,
                plan=default_plan,
                start_date=timezone.now(),
                end_date=timezone.now() + timedelta(days=default_plan.duration_days),
                is_active=True
            )

            # Связываем подписку с устройством через SubscriptionDevice
            SubscriptionDevice.objects.create(
                subscription=subscription,
                device=user_device
            )

            # Создаем токены и сессию через helper-функцию
            tokens = _create_tokens_and_session(user_account, user_device)

            # Формируем информацию о подписке
            subscription_info = {
                'plan_name': default_plan.name,
                'expires_at': subscription.end_date.isoformat()
            }

            logger.info(f"New anonymous device registered: {device_id} for user {user_account.id}")

            return Response({
                **tokens,
                'device_secret': str(user_device.device_secret),
                'subscription': subscription_info
            }, status=status.HTTP_201_CREATED)

    except Exception as e:
        logger.error(f"Error registering device {device_id}: {str(e)}")
        return Response({
            'error': 'Internal server error during device registration'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
