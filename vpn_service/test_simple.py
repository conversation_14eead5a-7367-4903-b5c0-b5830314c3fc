#!/usr/bin/env python3
"""
Простой тест для проверки логики эндпоинтов активации.
"""

import os
import sys
import django

# Настройка Django
sys.path.append('/root/matrix/vpn_service')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vpn_service.settings')
django.setup()

from django.test import TestCase
from django.contrib.auth import get_user_model
from accounts.models import UserAccount, UserDevice, ActivationCode, HiddifyLink
from subscriptions.models import SubscriptionPlan, ActiveSubscription
from promo.models import PromoCode
from django.utils import timezone
from datetime import timedelta

def test_activation_code_generation():
    """Тестирует генерацию кода активации."""
    print("🔧 Тестирование генерации кода активации...")

    # Создаем тестовые данные
    plan, created = SubscriptionPlan.objects.get_or_create(
        name="Test Plan",
        defaults={
            'description': "Test plan",
            'price': 10.00,
            'currency': 'USD',
            'duration_days': 30,
            'traffic_limit_gb': 100,
            'max_devices': 3,
            'is_active': True
        }
    )

    user, created = UserAccount.objects.get_or_create(
        email="<EMAIL>",
        defaults={
            'username': '<EMAIL>',
            'is_anonymous': False
        }
    )
    if created:
        user.set_password("testpass123")
        user.save()

    device, created = UserDevice.objects.get_or_create(
        user=user,
        device_id="test-device-123",
        defaults={
            'device_name': "Test Device",
            'is_active': True
        }
    )
    
    # Создаем активную подписку
    subscription, created = ActiveSubscription.objects.get_or_create(
        user=user,
        is_active=True,
        defaults={
            'plan': plan,
            'start_date': timezone.now(),
            'end_date': timezone.now() + timedelta(days=30)
        }
    )
    
    print(f"✅ Создан пользователь: {user.email}")
    print(f"✅ Создана подписка: {subscription.plan.name}")
    print(f"✅ Лимит устройств: {plan.max_devices}")
    print(f"✅ Текущих устройств: {UserDevice.objects.filter(user=user, is_active=True).count()}")
    
    # Проверяем логику генерации кода
    current_devices = UserDevice.objects.filter(user=user, is_active=True).count()
    if current_devices >= plan.max_devices:
        print("❌ Достигнут лимит устройств")
        return False
    else:
        print("✅ Можно генерировать код активации")
        
        # Создаем код активации
        import random
        import string
        code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
        
        activation_code = ActivationCode.objects.create(
            code=code,
            user=user,
            expires_at=timezone.now() + timedelta(minutes=10),
            is_active=True
        )
        
        print(f"✅ Код активации создан: {activation_code.code}")
        return True

def test_promo_code_activation():
    """Тестирует активацию промокода."""
    print("\n💳 Тестирование активации промокода...")

    # Создаем тестовые данные
    plan, created = SubscriptionPlan.objects.get_or_create(
        name="Premium Plan",
        defaults={
            'description': "Premium plan",
            'price': 20.00,
            'currency': 'USD',
            'duration_days': 30,
            'traffic_limit_gb': 200,
            'max_devices': 5,
            'is_active': True
        }
    )

    user, created = UserAccount.objects.get_or_create(
        email="<EMAIL>",
        defaults={
            'username': '<EMAIL>',
            'is_anonymous': False
        }
    )
    if created:
        user.set_password("testpass123")
        user.save()

    # Создаем промокод (удаляем старый если есть)
    PromoCode.objects.filter(code="TESTPROMO123").delete()
    promo_code = PromoCode.objects.create(
        code="TESTPROMO123",
        plan=plan,
        is_activated=False,
        expires_at=timezone.now() + timedelta(days=30)
    )

    print(f"✅ Создан промокод: {promo_code.code}")
    print(f"✅ Тарифный план: {plan.name}")
    print(f"✅ Промокод активирован: {promo_code.is_activated}")
    print(f"✅ Промокод истек: {promo_code.is_expired()}")
    print(f"✅ План активен: {promo_code.plan.is_active}")
    print(f"✅ Промокод валиден: {promo_code.is_valid()}")
    
    # Проверяем активацию
    if promo_code.is_valid():
        # Деактивируем старые подписки
        ActiveSubscription.objects.filter(user=user, is_active=True).update(is_active=False)
        
        # Создаем новую подписку
        new_subscription = ActiveSubscription.objects.create(
            user=user,
            plan=plan,
            start_date=timezone.now(),
            end_date=timezone.now() + timedelta(days=plan.duration_days),
            is_active=True,
            payment_method='promo_code'
        )
        
        # Активируем промокод
        promo_code.activate(user)
        
        print(f"✅ Подписка создана: {new_subscription.plan.name}")
        print(f"✅ Промокод активирован: {promo_code.is_activated}")
        return True
    else:
        print("❌ Промокод недействителен")
        return False

def main():
    """Основная функция тестирования."""
    print("🚀 Запуск простых тестов логики активации")
    print("=" * 50)
    
    try:
        # Тест 1: Генерация кода активации
        result1 = test_activation_code_generation()
        
        # Тест 2: Активация промокода
        result2 = test_promo_code_activation()
        
        print("\n" + "=" * 50)
        if result1 and result2:
            print("🎉 Все тесты прошли успешно!")
        else:
            print("❌ Некоторые тесты не прошли")
        
    except Exception as e:
        print(f"❌ Ошибка во время тестирования: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
